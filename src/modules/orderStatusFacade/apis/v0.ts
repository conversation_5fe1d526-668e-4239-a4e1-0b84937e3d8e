import { getMicroserviceEndpoint, InternalV1EndpointOptions } from "@api-routes/endpoints";
import { fetcher } from "@utils/fetcher";
import { IFetchOptions } from "itsf-ui-common";
import { IAssignmentCompleteRequestDto, TCreateOrderPayload } from "../interfaces/payloads";
import { IOrderStatusFacadeCreateOrderVisitResponse } from "../interfaces/responses/IWFEOrderFacadeCreateOrderResponse";
import { IOrderStatusFacadeAssigmentCompleteResponse } from "../interfaces/responses/IWFEOrderFacadeAssigmentCompleteResponse";

const OSF_ENDPOINT = "v2/notification-service/";
const OSF_PRIVATE_ENDPOINT = getMicroserviceEndpoint(OSF_ENDPOINT, InternalV1EndpointOptions);
const OSF_PRIVATE_ENDPOINT_TEST = "http://127.0.0.1:13419/";


export const createOrderVisit = (
    orderID: string,
    payload: TCreateOrderPayload,
    options: IFetchOptions = {}
) => {
    const url = new URL(`${OSF_PRIVATE_ENDPOINT}field-service/order/${orderID}`);

    return fetcher<IOrderStatusFacadeCreateOrderVisitResponse>(url.toString(), {
        method: "POST",
        body: payload,
        ...options,
    });
};


export const assigmentComplete = (payload: IAssignmentCompleteRequestDto, options: IFetchOptions = {}) => {
    const url = new URL(`${OSF_PRIVATE_ENDPOINT}field-service/order-visits`);

    return fetcher<IOrderStatusFacadeAssigmentCompleteResponse>(url.toString(), {
        method: "POST",
        body: payload,
        ...options,
    });
};
