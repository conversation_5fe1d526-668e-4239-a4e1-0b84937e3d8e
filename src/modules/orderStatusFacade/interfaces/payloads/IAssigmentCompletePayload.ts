export interface IAssignmentCompleteRequestDto {
  /** Event generated by */
  eventGeneratedBy?: string;

  /** Event generated at timestamp (ISO 8601) */
  eventGeneratedAt?: string;

  /** Work order details */
  workOrder?: WorkOrderDto;

  /** Appointment information */
  appointment?: AppointmentDto;

  /** Customer data information */
  customerData?: CustomerDataDto;
}

export interface WorkOrderDto {
  wochannelID?: string;
  operativeArea?: string;
  country?: string;
  city?: string;
  territory?: string;
  fieldEngComment?: string;
  posaddress?: string;
  posconnectionData4?: string;
  posconnectionData3?: string;
  posconnectionData2?: string;
  posconnectionData1?: string;
  wireUsageEnd?: string;
  wireUsageStart?: string;
  creationCauseReason?: string;
  clossingCauseReason?: string;
  clossingComment?: string;
  channelComment?: string;
  laboresUsed?: LaborUsedDto[];
  noMaterialUsed?: boolean;
  materialUsed?: MaterialUsedDto[];
  noEquipmentUsed?: boolean;
  equipmentUsed?: EquipmentUsedDto[];
  noEquipmentCollected?: boolean;
  equipmentCollected?: object[];
  services?: ServiceDto[];
  woclosureGeoToken?: string;
  woclosureGeoTokenEntered?: string;
  woclosureLatitude?: string;
  woclosureLongitude?: string;
  priority?: number;
  wostatus?: string;
  localWOType?: WorkOrderTypeDto;
  wotype?: WorkOrderTypeDto;
  wotypeCategory?: WorkOrderTypeDto;
  wiFiCertGenerated?: boolean;
  wiFiCertID?: string;
  wiFiCertResult?: string;
}

export interface AppointmentDto {
  engineers?: EngineerDto[];
  appointmentFinish?: string;
  appointmentStart?: string;
  assigmentFinish?: string;
  assigmentStart?: string;
  dueDate?: string;
  duration?: number;
  earlyStart?: string;
  appointmentConfirmationStatus?: string;
  appointmentConfirmationDate?: string;
  appointmentComment?: string;
}

export interface CustomerDataDto {
  customerExternalReferenceID?: string;
  customerFirstName?: string;
  customerLastName?: string;
  customerCompanyName?: string;
  customerType?: string;
  customerDocumentID?: string;
  contactName?: string;
  contactPhoneNumber?: string;
  contacteMail?: string;
  customerAddress?: string;
  customerCode?: string;
}

export interface EngineerDto {
  id?: string;
  docID?: string;
  name?: string;
  contractorID?: string;
}

export interface LaborUsedDto {
  externalRefID?: string;
  laborType?: LaborTypeDto;
  quantityUsed?: number;
}

export interface LaborTypeDto {
  code?: string;
  name?: string;
}

export interface MaterialUsedDto {
  materialType?: MaterialTypeDto;
  description?: string;
  externalRefID?: string;
  id?: string;
  quantityUsed?: number;
  unit?: string;
}

export interface MaterialTypeDto {
  externalRefID?: string;
  code?: string;
  name?: string;
}

export interface EquipmentUsedDto {
  equipmentType?: EquipmentTypeDto;
  description?: string;
  externalRefID?: string;
  id?: string;
  serialNumber?: string;
  service?: ServiceDto;
  operationalCertGenerated?: boolean;
  opCertID?: number;
  opCertResult?: string;
}

export interface EquipmentTypeDto {
  externalRefID?: string;
  code?: string;
  name?: string;
}

export interface ServiceDto {
  serviceExternalRefID?: string;
  serviceName?: string;
  serviceCode?: string;
  completionCode?: string;
}

export interface WorkOrderTypeDto {
  code?: string;
  name?: string;
}
