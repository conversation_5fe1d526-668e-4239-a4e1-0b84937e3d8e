//import { EOrderStatusFacadeUseCase, EOrderStatusFacadeBusinessStatus } from "@modules/orderStatusFacade/constants";

export type TCreateOrderPayload =
    | ICreateOrderFakePayload;


interface ICreateOrderFakePayload {    
    orderId: string;
    visitId: string;
    useCase: string;//EOrderStatusFacadeUseCase.INSTALLATION;
    accountId: string;
    clientId: string;
    activity: string;
    businessStatus: string; // EOrderStatusFacadeBusinessStatus.SCHEDULED;
}

